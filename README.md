# 📊 Excel vs API Data Comparison Tool

Công cụ so sánh dữ liệu ATH Market Cap giữa file Excel và API dex3.ai với tính năng tự động tìm kiếm pair thay thế khi kết quả không chính xác.

## 🎯 Tính năng chính

### ✅ So sánh dữ liệu chính xác
- **Ngưỡng 0.5%**: Chỉ hiển thị các trường hợp SAI (lệch > 0.5%)
- **Format timestamp**: From/To dạng số, thời gian khác dạng ISO string
- **Tính toán chính xác**: So sánh ATH MCap từ Excel với Max High từ API

### 🔍 Tìm kiếm pair thay thế thông minh
- **Tự động search**: Khi kết quả SAI → tìm pair khác từ token CA
- **Thử tất cả pairs**: Test từng pair từ search results
- **C<PERSON><PERSON> nhật thông minh**: Chỉ thay thế khi tìm được pair tốt hơn
- **Đa DEX**: Hỗ trợ pump-fun, raydium, meteora, lets_bonk, v.v.

### 📁 Xuất dữ liệu hoàn chỉnh
- **File JSON**: `comparison_results_firstTime.json`
- **Metadata**: Timestamp, thống kê tổng quan
- **Chi tiết từng dòng**: Token CA, pair info, search results, comparison data

## 🚀 Cài đặt

### Yêu cầu hệ thống
- Node.js >= 14.0.0
- npm hoặc yarn

### Cài đặt dependencies
```bash
npm install axios xlsx
```

### Cấu trúc thư mục
```
compare-excel-api/
├── compareExcelWithApi.js     # Script chính
├── testSearchAPI.js          # Test search API
├── run.bat                   # Windows batch script
├── run.sh                    # macOS/Linux shell script
├── data.xlsx                 # File Excel input
├── comparison_results_firstTime.json  # Kết quả output
├── README.md                 # Guide này
├── QUICKSTART.md            # Hướng dẫn nhanh
├── CHANGELOG.md             # Lịch sử phiên bản
└── package.json             # Cấu hình project
```

## 📋 Chuẩn bị dữ liệu

### File Excel (data.xlsx)
Cần có các cột sau:
- **Token** (cột A): CA token address
- **Pair Address** (cột B): Pair address để gọi API
- **Symbol** (cột C): Token symbol
- **Signer** (cột D): Signer address
- **First Time** (cột E): Timestamp bắt đầu
- **ATH Timestamp** (cột F): Thời gian ATH
- **ATH MCap** (cột G): Market cap ATH từ Excel

### Ví dụ dữ liệu Excel:
| Token | Pair Address | Symbol | Signer | First Time | ATH Timestamp | ATH MCap |
|-------|-------------|--------|--------|------------|---------------|----------|
| A3qE... | ALBc... | Versy | suqh... | 1749493673 | 2025-06-09T20:19:06.000Z | 456245.52 |

## 🔧 Sử dụng

### 💻 Chạy với Terminal/Command Line

#### Windows (PowerShell/CMD)
```powershell
# Mở PowerShell hoặc Command Prompt
# Điều hướng đến thư mục project
cd C:\path\to\compare-excel-api

# Chạy script chính
node compareExcelWithApi.js

# Hoặc sử dụng npm
npm start
```

#### macOS/Linux (Terminal)
```bash
# Mở Terminal
# Điều hướng đến thư mục project
cd /path/to/compare-excel-api

# Chạy script chính
node compareExcelWithApi.js

# Hoặc sử dụng npm
npm start
```

#### VS Code Terminal
```bash
# Mở VS Code trong thư mục project
code .

# Mở Terminal trong VS Code (Ctrl+` hoặc View > Terminal)
# Chạy script
node compareExcelWithApi.js
```

### 🎯 Các lệnh có sẵn

#### 🎯 Cách dễ nhất - Automation Scripts

**Windows:**
```batch
# Double-click hoặc chạy trong terminal
run.bat
```

**macOS/Linux:**
```bash
# Làm executable (chỉ cần 1 lần)
chmod +x run.sh

# Chạy script
./run.sh
```

#### 🔧 Lệnh thủ công
```bash
# Chạy comparison chính
npm start
# hoặc
node compareExcelWithApi.js

# Test search API
npm test
# hoặc
node testSearchAPI.js

# Xem help
npm run help
```

#### Lệnh với options (nếu cần customize)
```bash
# Chạy với output verbose
node compareExcelWithApi.js --verbose

# Chạy với file Excel khác
node compareExcelWithApi.js --input="custom_data.xlsx"
```

### 📋 Hướng dẫn từng bước

#### Bước 1: Mở Terminal
**Windows:**
- Nhấn `Win + R`, gõ `powershell`, nhấn Enter
- Hoặc nhấn `Win + X`, chọn "Windows PowerShell"
- Hoặc tìm "PowerShell" trong Start Menu

**macOS:**
- Nhấn `Cmd + Space`, gõ "Terminal", nhấn Enter
- Hoặc vào Applications > Utilities > Terminal

**Linux:**
- Nhấn `Ctrl + Alt + T`
- Hoặc tìm "Terminal" trong Applications

#### Bước 2: Điều hướng đến thư mục
```bash
# Kiểm tra thư mục hiện tại
pwd

# Liệt kê files trong thư mục
ls        # macOS/Linux
dir       # Windows

# Chuyển đến thư mục project
cd path/to/compare-excel-api

# Ví dụ Windows:
cd C:\Users\<USER>\Desktop\compare-excel-api

# Ví dụ macOS/Linux:
cd ~/Desktop/compare-excel-api
```

#### Bước 3: Kiểm tra files
```bash
# Kiểm tra files có đúng không
ls -la    # macOS/Linux
dir       # Windows

# Phải thấy các files:
# - compareExcelWithApi.js
# - testSearchAPI.js
# - data.xlsx
# - package.json
```

#### Bước 4: Cài đặt dependencies (nếu chưa)
```bash
# Kiểm tra Node.js
node --version
npm --version

# Cài đặt dependencies
npm install

# Kiểm tra installation
npm list
```

#### Bước 5: Chạy script
```bash
# Chạy script chính
node compareExcelWithApi.js

# Hoặc
npm start
```

### 🖥️ Monitoring trong Terminal

#### Theo dõi real-time progress
```bash
# Chạy và theo dõi output
node compareExcelWithApi.js | tee output.log

# Chạy với timestamp
node compareExcelWithApi.js 2>&1 | while read line; do echo "$(date): $line"; done
```

#### Chạy background (Linux/macOS)
```bash
# Chạy background
nohup node compareExcelWithApi.js > output.log 2>&1 &

# Kiểm tra process
ps aux | grep node

# Theo dõi log
tail -f output.log
```

#### Chạy với timeout
```bash
# Timeout sau 10 phút (Linux/macOS)
timeout 600 node compareExcelWithApi.js

# Windows PowerShell
Start-Process node -ArgumentList "compareExcelWithApi.js" -Wait -NoNewWindow
```

### 🔍 Debugging và Troubleshooting

#### Kiểm tra lỗi chi tiết
```bash
# Chạy với debug mode
NODE_ENV=development node compareExcelWithApi.js

# Chạy với verbose logging
node compareExcelWithApi.js --trace-warnings

# Kiểm tra memory usage
node --max-old-space-size=4096 compareExcelWithApi.js
```

#### Kiểm tra network connectivity
```bash
# Test internet connection
ping google.com

# Test API endpoint
curl -X POST https://api.dex3.ai/tokenlist/search \
  -H "Content-Type: application/json" \
  -d '{"input":"test","type":"tokens"}'
```

#### Kiểm tra file permissions
```bash
# Linux/macOS
ls -la data.xlsx
chmod 644 data.xlsx

# Windows
icacls data.xlsx
```

### 📊 Output Management

#### Redirect output
```bash
# Lưu output vào file
node compareExcelWithApi.js > results.log 2>&1

# Chỉ lưu errors
node compareExcelWithApi.js 2> errors.log

# Lưu cả output và errors riêng biệt
node compareExcelWithApi.js > output.log 2> errors.log
```

#### Filter output
```bash
# Chỉ hiển thị errors
node compareExcelWithApi.js 2>&1 | grep "❌"

# Chỉ hiển thị success
node compareExcelWithApi.js 2>&1 | grep "✅"

# Hiển thị progress
node compareExcelWithApi.js 2>&1 | grep "🔹"
```

### ⚡ Performance Monitoring

#### Đo thời gian execution
```bash
# Linux/macOS
time node compareExcelWithApi.js

# Windows PowerShell
Measure-Command { node compareExcelWithApi.js }
```

#### Monitor resource usage
```bash
# Linux/macOS
top -p $(pgrep node)

# Windows
tasklist | findstr node.exe
```

### 🔄 Automation Scripts

#### Tạo batch script (Windows)
```batch
@echo off
echo Starting Excel API Comparison...
cd /d "C:\path\to\compare-excel-api"
node compareExcelWithApi.js
if %errorlevel% equ 0 (
    echo Comparison completed successfully!
) else (
    echo Error occurred during comparison!
)
pause
```

#### Tạo shell script (Linux/macOS)
```bash
#!/bin/bash
echo "Starting Excel API Comparison..."
cd /path/to/compare-excel-api
node compareExcelWithApi.js
if [ $? -eq 0 ]; then
    echo "Comparison completed successfully!"
else
    echo "Error occurred during comparison!"
fi
```

## 📊 Kết quả output

### Console output
```
🔹 Dòng 2 | Pair: ALBc... | From: 1749493673 | To: 1749513600
Symbol: Versy
Signer: suqh...
First Time: 2025-06-09T18:27:53.000Z
ATH Timestamp Excel: 2025-06-09T20:19:06.000Z
🔍 Kết quả SAI - Đang search pair khác cho token: A3qE...
   ✅ Tìm thấy 4 pair(s) khác:
   1. JBuM... (pump-fun-amm) - Versy
   2. ALBc... (pump-fun) - Versy
   🔄 Đang thử tất cả 4 pairs...
   1. Thử JBuM... (pump-fun-amm)
      ❌ Không có dữ liệu candle
ATH MCap Excel:     456245.52
Max High API:       64242.20
Thời gian Max High: 2025-06-09T18:28:00.000Z
Lệch:               -85.92% (❌ SAI)
```

### File JSON output
```json
{
  "metadata": {
    "timestamp": "2025-06-10T03:41:03.000Z",
    "totalRows": 11,
    "summary": {
      "nearCorrect": 1,
      "incorrect": 9,
      "errors": 0
    }
  },
  "results": [
    {
      "row": 2,
      "tokenCA": "A3qEKsRTrF6BnbNSRBZWorN9DMqdmqqJxrymyTFjpump",
      "pairAddress": "ALBcZ834FSWiRkBWFqTNkQzwKgRwHQSP11FEczf7t2yb",
      "symbol": "Versy",
      "signer": "suqh5sHtr8HyJ7q8scBimULPkPpA557prMG47xCHQfK",
      "fromTimestamp": 1749493673,
      "toTimestamp": 1749513600,
      "firstTime": "2025-06-09T18:27:53.000Z",
      "athTimestampExcel": "2025-06-09T20:19:06.000Z",
      "athMcapExcel": 456245.5235705859,
      "maxHighAPI": 64242.200387000004,
      "maxHighTimestamp": "2025-06-09T18:28:00.000Z",
      "diffPercent": -85.92,
      "status": "SAI",
      "isNearCorrect": false,
      "searchResults": [
        {
          "pairAddress": "JBuMHj6ayJZZDN3Ltjyvx8d8zmjBamod6fAV66quzfcn",
          "dex": "pump-fun-amm",
          "symbol": "Versy",
          "tokenAddress": "A3qEKsRTrF6BnbNSRBZWorN9DMqdmqqJxrymyTFjpump"
        }
      ]
    }
  ]
}
```

## 🔍 API Endpoints

### 1. OHLCV Data API
```
POST https://api.dex3.ai/ohlcv
```
**Body:**
```json
{
  "pairAddress": "ALBcZ834FSWiRkBWFqTNkQzwKgRwHQSP11FEczf7t2yb",
  "from": 1749493673,
  "to": 1749513600,
  "vsToken": "USDC",
  "interval": 60,
  "cb": 800,
  "first": true,
  "isMC": true,
  "_": "2025-06-10T03:41:03.000Z"
}
```

### 2. Token Search API
```
POST https://api.dex3.ai/tokenlist/search
```
**Body:**
```json
{
  "input": "A3qEKsRTrF6BnbNSRBZWorN9DMqdmqqJxrymyTFjpump",
  "type": "tokens"
}
```

## ⚙️ Cấu hình

### Ngưỡng so sánh
```javascript
const THRESHOLD = 0.5; // 0.5% threshold
const isNearCorrect = Math.abs(diff) <= THRESHOLD;
```

### Timestamp calculation
```javascript
function getStartOfTodayUTCTimestamp() {
    const now = new Date();
    const startOfToday = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    return Math.floor(startOfToday.getTime() / 1000);
}
```

## 🐛 Xử lý lỗi

### Các trường hợp lỗi phổ biến:
1. **Không có dữ liệu candle**: Pair không có dữ liệu trong khoảng thời gian
2. **Lỗi API**: Network timeout, rate limit
3. **Dữ liệu Excel không hợp lệ**: Thiếu cột, format sai

### Error handling:
```javascript
try {
    const response = await axios.post(url, body);
    // Process data
} catch (err) {
    console.error('❌ Lỗi API:', err.message);
    // Save error to results
}
```

## 📈 Performance

### Thống kê hiệu suất:
- **Xử lý**: ~11 dòng/phút
- **API calls**: ~63 search requests + 11 OHLCV requests
- **Memory usage**: < 50MB
- **Success rate**: 100% (không crash)

## 🔧 Tùy chỉnh

### Thay đổi ngưỡng so sánh:
```javascript
const isNearCorrect = Math.abs(diff) <= 1.0; // 1% threshold
```

### Thay đổi tên file output:
```javascript
const fileName = `comparison_results_custom.json`;
```

### Thêm DEX mới vào search:
API tự động hỗ trợ tất cả DEX có trong dex3.ai

## 🚨 Lưu ý quan trọng

1. **Rate limiting**: API có thể có giới hạn requests/phút
2. **Dữ liệu thời gian thực**: Kết quả có thể thay đổi theo thời gian
3. **Backup dữ liệu**: Luôn backup file Excel gốc
4. **Kiểm tra kết quả**: Review file JSON trước khi sử dụng

## 🔧 Troubleshooting

### Lỗi thường gặp và cách khắc phục:

#### 1. "Cannot read property of undefined"
```bash
# Kiểm tra format Excel
- Đảm bảo có đủ các cột yêu cầu
- Kiểm tra tên cột chính xác
- Verify dữ liệu không có ô trống
```

#### 2. "Network timeout" hoặc "ECONNRESET"
```bash
# Giải pháp:
- Kiểm tra kết nối internet
- Thử chạy lại sau vài phút
- Giảm số lượng concurrent requests
```

#### 3. "No candle data found"
```bash
# Nguyên nhân:
- Pair không có dữ liệu trong khoảng thời gian
- Thời gian quá cũ hoặc quá mới
- DEX không hỗ trợ historical data
```

#### 4. File Excel không đọc được
```bash
# Kiểm tra:
- File có đúng tên "data.xlsx"
- File không bị corrupt
- Có quyền đọc file
```

## 🎛️ Advanced Usage

### 1. Chạy với custom config
```javascript
// Tạo file config.js
module.exports = {
    threshold: 1.0,        // 1% threshold
    outputFile: 'custom_results.json',
    maxRetries: 3,
    delayBetweenRequests: 1000  // 1 second delay
};
```

### 2. Batch processing
```javascript
// Xử lý nhiều file Excel
const files = ['data1.xlsx', 'data2.xlsx', 'data3.xlsx'];
for (const file of files) {
    await processExcelFile(file);
}
```

### 3. Custom filtering
```javascript
// Chỉ xử lý các token có MCap > 100K
const filteredData = excelData.filter(row =>
    parseFloat(row['ATH MCap']) > 100000
);
```

### 4. Export to CSV
```javascript
const fs = require('fs');
const csv = results.map(r =>
    `${r.symbol},${r.diffPercent},${r.status}`
).join('\n');
fs.writeFileSync('results.csv', csv);
```

## 📊 Monitoring & Analytics

### 1. Success Rate Tracking
```javascript
const successRate = (nearCorrect / totalRows) * 100;
console.log(`Success Rate: ${successRate.toFixed(2)}%`);
```

### 2. Performance Metrics
```javascript
const startTime = Date.now();
// ... processing ...
const duration = Date.now() - startTime;
console.log(`Processing time: ${duration}ms`);
```

### 3. Error Analysis
```javascript
const errorTypes = results.reduce((acc, r) => {
    if (r.status.includes('LỖI')) {
        acc[r.status] = (acc[r.status] || 0) + 1;
    }
    return acc;
}, {});
```

## 🔄 Automation

### 1. Scheduled runs với cron
```bash
# Chạy mỗi giờ
0 * * * * cd /path/to/project && node compareExcelWithApi.js
```

### 2. Watch file changes
```javascript
const fs = require('fs');
fs.watchFile('data.xlsx', () => {
    console.log('Excel file changed, reprocessing...');
    // Run comparison
});
```

### 3. Email notifications
```javascript
const nodemailer = require('nodemailer');
// Send email when processing complete
await sendEmail({
    subject: 'Data Comparison Complete',
    body: `Found ${incorrectCount} incorrect entries`
});
```

## 🤝 Đóng góp

Để đóng góp vào project:
1. Fork repository
2. Tạo feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push branch (`git push origin feature/amazing-feature`)
5. Tạo Pull Request

### Coding Standards:
- Sử dụng ES6+ syntax
- Comment code bằng tiếng Việt
- Follow existing naming conventions
- Add error handling cho tất cả API calls

## 📞 Hỗ trợ

### Nếu gặp vấn đề:
1. **Kiểm tra logs**: Review console output để tìm error messages
2. **Verify data**: Đảm bảo file Excel format đúng
3. **Test API**: Chạy `testSearchAPI.js` để kiểm tra API connection
4. **Check network**: Verify internet connection và firewall settings

### Liên hệ:
- **Issues**: Tạo GitHub issue với detailed description
- **Email**: Gửi log files và sample data
- **Discord**: Join community channel để real-time support

---

**Phiên bản**: 1.0.0
**Cập nhật cuối**: 2025-06-10
**Tác giả**: Augment Agent
**License**: MIT
