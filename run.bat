@echo off
title Excel API Data Comparison Tool
color 0A

echo.
echo ========================================
echo   Excel API Data Comparison Tool
echo ========================================
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js is not installed!
    echo Please download and install Node.js from https://nodejs.org
    echo.
    pause
    exit /b 1
)

echo ✅ Node.js detected: 
node --version

REM Check if npm is available
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm is not available!
    pause
    exit /b 1
)

echo ✅ npm detected: 
npm --version
echo.

REM Check if package.json exists
if not exist "package.json" (
    echo ❌ package.json not found!
    echo Make sure you're in the correct directory.
    echo.
    pause
    exit /b 1
)

REM Check if data.xlsx exists
if not exist "data.xlsx" (
    echo ❌ data.xlsx not found!
    echo Please make sure the Excel file exists in the current directory.
    echo.
    pause
    exit /b 1
)

echo ✅ Excel file found: data.xlsx

REM Check if node_modules exists, if not install dependencies
if not exist "node_modules" (
    echo.
    echo 📦 Installing dependencies...
    npm install
    if %errorlevel% neq 0 (
        echo ❌ Failed to install dependencies!
        pause
        exit /b 1
    )
    echo ✅ Dependencies installed successfully!
) else (
    echo ✅ Dependencies already installed
)

echo.
echo 🚀 Starting Excel API Data Comparison...
echo.
echo ⏳ This may take a few minutes depending on your data size...
echo 💡 You can press Ctrl+C to stop the process anytime
echo.

REM Run the main script
node compareExcelWithApi.js

REM Check if the script ran successfully
if %errorlevel% equ 0 (
    echo.
    echo ========================================
    echo ✅ Comparison completed successfully!
    echo ========================================
    echo.
    echo 📁 Results saved to: comparison_results_firstTime.json
    echo.
    
    REM Check if result file exists
    if exist "comparison_results_firstTime.json" (
        echo 📊 Result file size:
        for %%A in (comparison_results_firstTime.json) do echo    %%~zA bytes
        echo.
        echo 💡 You can now open the JSON file to view detailed results
    )
) else (
    echo.
    echo ========================================
    echo ❌ An error occurred during comparison!
    echo ========================================
    echo.
    echo 🔍 Please check the error messages above
    echo 💡 Common solutions:
    echo    - Check your internet connection
    echo    - Verify Excel file format
    echo    - Make sure all required columns exist
    echo.
)

echo.
echo Press any key to exit...
pause >nul
