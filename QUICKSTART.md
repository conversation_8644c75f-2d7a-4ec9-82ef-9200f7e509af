# 🚀 Quick Start Guide

Hướng dẫn nhanh để bắt đầu sử dụng Excel vs API Data Comparison Tool.

## ⚡ 5 phút để chạy

### 🖥️ Bước 1: Mở Terminal

**Windows:**
```powershell
# Nhấn Win + R, gõ "powershell", nhấn Enter
# Hoặc tìm "PowerShell" trong Start Menu
```

**macOS:**
```bash
# Nhấn Cmd + Space, gõ "Terminal", nhấn Enter
# Hoặc Applications > Utilities > Terminal
```

**Linux:**
```bash
# Nhấn Ctrl + Alt + T
```

### 📁 Bước 2: Điều hướng đến thư mục
```bash
# Kiểm tra thư mục hiện tại
pwd

# Chuyển đến thư mục project
cd path/to/compare-excel-api

# Ví dụ:
cd C:\Users\<USER>\Desktop\compare-excel-api  # Windows
cd ~/Desktop/compare-excel-api              # macOS/Linux
```

### 📦 Bước 3: Cài đặt dependencies
```bash
# Kiểm tra Node.js có sẵn
node --version
npm --version

# Cài đặt packages
npm install

# Kiểm tra installation thành công
npm list
```

### 📊 Bước 4: Chuẩn bị file Excel
Đảm bảo file `data.xlsx` có các cột:
- **Token** (A): CA token address
- **Pair Address** (B): Pair address
- **Symbol** (C): Token symbol
- **Signer** (D): Signer address
- **First Time** (E): Timestamp
- **ATH Timestamp** (F): ATH time
- **ATH MCap** (G): ATH market cap

```bash
# Kiểm tra file Excel có tồn tại
ls data.xlsx     # macOS/Linux
dir data.xlsx    # Windows
```

### 🚀 Bước 5: Chạy comparison

#### 🎯 Cách dễ nhất - Sử dụng script có sẵn

**Windows:**
```batch
# Double-click file run.bat
# Hoặc chạy trong Command Prompt/PowerShell
run.bat
```

**macOS/Linux:**
```bash
# Làm file executable (chỉ cần 1 lần)
chmod +x run.sh

# Chạy script
./run.sh
```

#### 🔧 Cách thủ công
```bash
# Cách 1: Sử dụng npm (khuyến nghị)
npm start

# Cách 2: Chạy trực tiếp
node compareExcelWithApi.js

# Cách 3: Với output logging
node compareExcelWithApi.js | tee output.log
```

### 📋 Bước 6: Xem kết quả
- **Console**: Hiển thị real-time progress
- **File JSON**: `comparison_results_firstTime.json`

```bash
# Kiểm tra file kết quả được tạo
ls -la comparison_results_firstTime.json     # macOS/Linux
dir comparison_results_firstTime.json       # Windows
```

## 📊 Hiểu kết quả

### Console Output
```
🔹 Dòng 2 | Pair: ALBc... | From: 1749493673 | To: 1749513600
Symbol: Versy
🔍 Kết quả SAI - Đang search pair khác...
   ✅ Tìm thấy 4 pair(s) khác
ATH MCap Excel:     456245.52
Max High API:       64242.20
Lệch:               -85.92% (❌ SAI)
```

### Ý nghĩa:
- **✅ GẦN ĐÚNG**: Lệch ≤ 0.5%
- **❌ SAI**: Lệch > 0.5%
- **🔍 Search**: Tự động tìm pair thay thế

## 🎯 Các lệnh hữu ích

```bash
# Chạy comparison chính
npm start

# Test search API
npm test

# Xem help
npm run help

# Chạy trực tiếp
node compareExcelWithApi.js
```

## 🔧 Tùy chỉnh nhanh

### Thay đổi ngưỡng (trong code)
```javascript
const isNearCorrect = Math.abs(diff) <= 1.0; // 1% thay vì 0.5%
```

### Thay đổi tên file output
```javascript
const fileName = `my_results.json`;
```

## ⚠️ Troubleshooting Terminal

### 🚫 Lỗi "node: command not found"
```bash
# Kiểm tra Node.js đã cài đặt chưa
node --version

# Nếu chưa có, tải từ https://nodejs.org
# Hoặc cài qua package manager:

# Windows (Chocolatey)
choco install nodejs

# macOS (Homebrew)
brew install node

# Linux (Ubuntu/Debian)
sudo apt update
sudo apt install nodejs npm
```

### 📁 Lỗi "Cannot find module"
```bash
# Đảm bảo đang ở đúng thư mục
pwd
ls package.json  # Phải thấy file này

# Cài đặt lại dependencies
rm -rf node_modules package-lock.json  # macOS/Linux
rmdir /s node_modules & del package-lock.json  # Windows
npm install
```

### 📊 Lỗi "Cannot read Excel file"
```bash
# Kiểm tra file tồn tại
ls -la data.xlsx     # macOS/Linux
dir data.xlsx        # Windows

# Kiểm tra permissions
chmod 644 data.xlsx  # macOS/Linux

# Đảm bảo file không mở trong Excel
# Đóng Excel trước khi chạy script
```

### 🌐 Lỗi "Network timeout"
```bash
# Test internet connection
ping google.com

# Test API endpoint
curl -X POST https://api.dex3.ai/tokenlist/search \
  -H "Content-Type: application/json" \
  -d '{"input":"test","type":"tokens"}'

# Nếu có proxy, set environment variables
export HTTP_PROXY=http://proxy:port
export HTTPS_PROXY=http://proxy:port
```

### 🔍 Debug mode
```bash
# Chạy với debug để xem chi tiết lỗi
NODE_ENV=development node compareExcelWithApi.js

# Hoặc với verbose logging
node compareExcelWithApi.js --trace-warnings

# Lưu log để phân tích
node compareExcelWithApi.js > debug.log 2>&1
```

### 💾 Lỗi "Permission denied"
```bash
# macOS/Linux - Fix permissions
sudo chown $USER:$USER -R .
chmod 755 .

# Windows - Chạy PowerShell as Administrator
# Right-click PowerShell > "Run as Administrator"
```

### 🔄 Script bị treo
```bash
# Dừng script
Ctrl + C

# Kiểm tra processes đang chạy
ps aux | grep node     # macOS/Linux
tasklist | findstr node  # Windows

# Kill process nếu cần
kill -9 <process_id>   # macOS/Linux
taskkill /F /PID <process_id>  # Windows
```

## 📈 Kết quả mong đợi

### Thành công:
```
📊 TỔNG KẾT:
✅ Gần đúng: 1
❌ Sai: 9  
⚠️ Lỗi: 0
📁 Đã xuất kết quả ra file: comparison_results_firstTime.json
```

### File JSON được tạo với:
- Metadata và thống kê
- Chi tiết từng dòng comparison
- Search results cho mỗi token
- Timestamp và performance metrics

## 🎉 Hoàn thành!

Bây giờ bạn có:
- ✅ Kết quả so sánh chi tiết
- ✅ Danh sách pairs thay thế
- ✅ Phân tích độ chính xác
- ✅ Dữ liệu JSON để xử lý tiếp

## 📚 Đọc thêm

- [README.md](README.md) - Hướng dẫn chi tiết
- [CHANGELOG.md](CHANGELOG.md) - Lịch sử phiên bản
- [testSearchAPI.js](testSearchAPI.js) - Test search functionality

---

**Cần hỗ trợ?** Xem [Troubleshooting section](README.md#-troubleshooting) trong README.
