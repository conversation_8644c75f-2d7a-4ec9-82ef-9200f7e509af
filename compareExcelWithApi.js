const axios = require('axios');
const ExcelJS = require('exceljs');
const fs = require('fs');

// Hàm lấy timestamp đầu ngày mai (UTC) - 12:00:00 AM của ngày tiếp theo
function getStartOfTomorrowUTCTimestamp() {
    // Tạo ngày cụ thể: June 12, 2025 12:00:00 AM UTC
    const targetDate = new Date('2025-06-12T00:00:00.000Z');
    return Math.floor(targetDate.getTime() / 1000);
}

const toTimestamp = getEndOfTodayUTCTimestamp();
console.log('Timestamp cuối ngày hôm nay (UTC):', toTimestamp);

// Mảng để lưu kết quả
const results = [];

// Đọc dữ liệu từ file Excel
async function readExcelFile() {
    const workbook = new ExcelJS.Workbook();
    await workbook.xlsx.readFile('./Data.xlsx');
    const worksheet = workbook.getWorksheet(1);
    
    const excelData = [];
    const headers = [];
    
    // Lấy headers từ dòng đầu tiên
    worksheet.getRow(1).eachCell((cell, colNumber) => {
        headers[colNumber] = cell.value;
    });
    
    // Lấy dữ liệu từ các dòng tiếp theo
    worksheet.eachRow((row, rowNumber) => {
        if (rowNumber > 1) { // Bỏ qua header row
            const rowData = {};
            row.eachCell((cell, colNumber) => {
                const header = headers[colNumber];
                if (header) {
                    rowData[header] = cell.value;
                }
            });
            excelData.push(rowData);
        }
    });
    
    return excelData;
}

// Hàm gọi API để lấy dữ liệu candle
async function fetchCandleData(pairAddress, from, to) {
    const url = "https://api.dex3.ai/ohlcv";
    const body = {
        pairAddress: pairAddress,
        from: from,
        to: to,
        vsToken: "USDC",
        interval: 60,
        cb: 800,
        first: true,
        isMC: true,
        _: new Date().toISOString()
    };

    try {
        const res = await axios.post(url, body, {
            headers: {
                "Content-Type": "application/json"
            }
        });

        return res.data?.data || [];
    } catch (err) {
        console.error(`❌ Lỗi API cho pair ${pairAddress}:`, err.message);
        return [];
    }
}

async function callApiForEachRow() {
    const excelData = await readExcelFile();
    
    for (const [index, row] of excelData.entries()) {
        const tokenCA = row['Token']; // Cột đầu tiên - CA token
        const pairAddress = row['Pair Address'];
        let firstTime = row['First Time'];

        // Chuyển First Time về unix timestamp (giây)
        let fromTimestamp;
        if (typeof firstTime === 'number') {
            fromTimestamp = firstTime;
        } else {
            fromTimestamp = Math.floor(new Date(firstTime).getTime() / 1000);
        }

        // Nếu thiếu dữ liệu thì bỏ qua dòng đó
        if (!pairAddress || !fromTimestamp) {
            console.warn(`⚠️ Dòng ${index + 2}: Thiếu Pair Address hoặc First Time`);
            continue;
        }

        const symbol = row['Symbol'];
        const signer = row['Signer'];
        const athTimestampExcel = row['ATH Timestamp'];

        // Chuyển đổi First Time thành string để hiển thị
        const firstTimeStr = typeof firstTime === 'number' 
            ? new Date(firstTime * 1000).toISOString()
            : firstTime;

        console.log(`\n🔹 Dòng ${index + 2} | Pair: ${pairAddress} | From: ${fromTimestamp} | To: ${toTimestamp}`);
        console.log(`Symbol: ${symbol}`);
        console.log(`Signer: ${signer}`);
        console.log(`First Time: ${firstTimeStr}`);
        console.log(`ATH Timestamp Excel: ${athTimestampExcel}`);

        try {
            const candles = await fetchCandleData(pairAddress, fromTimestamp, toTimestamp);

            if (candles && candles.length > 0) {
                // Dữ liệu candle có format: [timestamp, open, high, low, close, volume]
                const maxHigh = Math.max(...candles.map(candle => parseFloat(candle[2])));
                const maxHighCandle = candles.find(candle => parseFloat(candle[2]) === maxHigh);
                const maxHighTimeString = new Date(maxHighCandle[0] * 1000).toISOString();

                const athMcapExcel = parseFloat(row['ATH MCap']);

                if (maxHigh !== null) {
                    const diff = athMcapExcel !== 0 ? (maxHigh - athMcapExcel) / athMcapExcel * 100 : 0;
                    const isNearCorrect = Math.abs(diff) <= 0.5;

                    // Lưu kết quả vào mảng
                    const result = {
                        row: index + 2,
                        tokenCA,
                        pairAddress,
                        symbol,
                        signer,
                        fromTimestamp,
                        toTimestamp,
                        firstTime: firstTimeStr,
                        athTimestampExcel,
                        athMcapExcel,
                        maxHighAPI: maxHigh,
                        maxHighTimestamp: maxHighTimeString,
                        diffPercent: parseFloat(diff.toFixed(2)),
                        status: isNearCorrect ? 'GẦN ĐÚNG' : 'SAI',
                        isNearCorrect
                    };

                    results.push(result);

                    // Hiển thị kết quả so sánh
                    console.log(`ATH MCap Excel:     ${athMcapExcel}`);
                    console.log(`Max High API:       ${maxHigh}`);
                    console.log(`Thời gian Max High: ${maxHighTimeString}`);
                    if (isNearCorrect) {
                        console.log(`Lệch:               ${diff.toFixed(2)}% (✅ GẦN ĐÚNG)`);
                    } else {
                        console.log(`Lệch:               ${diff.toFixed(2)}% (❌ SAI)`);
                    }
                } else {
                    console.log('⚠️ Không tìm thấy candle nào để lấy high!');

                    // Lưu kết quả lỗi vào mảng
                    const result = {
                        row: index + 2,
                        tokenCA,
                        pairAddress,
                        symbol,
                        signer,
                        fromTimestamp,
                        toTimestamp,
                        firstTime: firstTimeStr,
                        athTimestampExcel,
                        athMcapExcel,
                        maxHighAPI: null,
                        maxHighTimestamp: null,
                        diffPercent: null,
                        status: 'LỖI - KHÔNG TÌM THẤY CANDLE',
                        isNearCorrect: false
                    };
                    results.push(result);
                }

            } else {
                console.log('⚠️ Không tìm thấy candle nào để lấy high!');

                // Lưu kết quả lỗi vào mảng
                const result = {
                    row: index + 2,
                    tokenCA,
                    pairAddress,
                    symbol,
                    signer,
                    fromTimestamp,
                    toTimestamp,
                    firstTime: firstTimeStr,
                    athTimestampExcel,
                    athMcapExcel: parseFloat(row['ATH MCap']) || null,
                    maxHighAPI: null,
                    maxHighTimestamp: null,
                    diffPercent: null,
                    status: 'LỖI - KHÔNG TÌM THẤY CANDLE',
                    isNearCorrect: false
                };
                results.push(result);
            }

        } catch (err) {
            if (err.response && err.response.data) {
                console.error('❌ Lỗi API:', JSON.stringify(err.response.data));
            } else {
                console.error('❌ Lỗi khác:', err.message);
            }

            // Lưu kết quả lỗi API vào mảng
            const result = {
                row: index + 2,
                tokenCA,
                pairAddress,
                symbol,
                signer,
                fromTimestamp,
                toTimestamp,
                firstTime: firstTimeStr,
                athTimestampExcel,
                athMcapExcel: parseFloat(row['ATH MCap']) || null,
                maxHighAPI: null,
                maxHighTimestamp: null,
                diffPercent: null,
                status: 'LỖI API',
                isNearCorrect: false,
                error: err.message
            };
            results.push(result);
        }
    }

    // Xuất kết quả ra file JSON
    const outputData = {
        metadata: {
            timestamp: new Date().toISOString(),
            totalRows: results.length,
            summary: {
                nearCorrect: results.filter(r => r.isNearCorrect).length,
                incorrect: results.filter(r => r.status === 'SAI').length,
                errors: results.filter(r => r.status.includes('LỖI')).length
            }
        },
        results: results
    };

    const fileName = `comparison_results_firstTime.json`;
    fs.writeFileSync(fileName, JSON.stringify(outputData, null, 2), 'utf8');

    console.log(`\n📊 TỔNG KẾT:`);
    console.log(`✅ Gần đúng: ${outputData.metadata.summary.nearCorrect}`);
    console.log(`❌ Sai: ${outputData.metadata.summary.incorrect}`);
    console.log(`⚠️ Lỗi: ${outputData.metadata.summary.errors}`);
    console.log(`📁 Đã xuất kết quả ra file: ${fileName}`);
}

callApiForEachRow();
