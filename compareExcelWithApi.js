const axios = require('axios');
const xlsx = require('xlsx');
const fs = require('fs');

// Đọ<PERSON> dữ liệu từ file Excel
const workbook = xlsx.readFile('./Data.xlsx');
const sheet = workbook.Sheets[workbook.SheetNames[0]];
const excelData = xlsx.utils.sheet_to_json(sheet);

// Hàm lấy timestamp 00:00:00 ngày hiện tại (UTC)
function getStartOfTodayUTCTimestamp() {
    const now = new Date();
    return Math.floor(Date.UTC(now.getUTCFullYear(), now.getUTCMonth(), now.getUTCDate()) / 1000);
}

const toTimestamp = getStartOfTodayUTCTimestamp();
console.log('Timestamp đầu ngày hôm nay (UTC):', toTimestamp);

// Mảng để lưu kết quả
const results = [];

// Hàm search pair address từ token CA
async function searchPairByToken(tokenCA) {
    const url = "https://api.dex3.ai/tokenlist/search";
    const body = {
        input: tokenCA,
        type: "tokens"
    };

    try {
        const res = await axios.post(url, body, {
            headers: {
                "Content-Type": "application/json"
            }
        });

        const data = res.data?.data || [];
        if (data.length === 0) {
            return null;
        }

        // Trả về tất cả pairs tìm được
        return data.map(pair => ({
            pairAddress: pair.pair_address,
            dex: pair.dex,
            symbol: pair.token_symbol,
            tokenAddress: pair.token_address
        }));
    } catch (err) {
        console.error(`❌ Lỗi search token ${tokenCA}:`, err.message);
        return null;
    }
}

// Hàm gọi API để lấy dữ liệu candle
async function fetchCandleData(pairAddress, fromTimestamp, toTimestamp) {
    const body = {
        "pairAddress": pairAddress,
        "timestamp": fromTimestamp,   
        "from": fromTimestamp,
        "to": toTimestamp,
        "vsToken": "USDC",
        "interval": 60,
        "cb": 400,
        "first": true,
        "isMC": true,
        //"_": new Date().toISOString()
    };

    try {
        const response = await axios.post(
            'https://api.dex3.ai/ohlcv',
            body,
            {
                headers: {
                    "Content-Type": "application/json"
                }
            }
        );
        return response.data?.data?.candles || [];
    } catch (err) {
        throw err;
    }
}

async function callApiForEachRow() {
    for (const [index, row] of excelData.entries()) {
        const tokenCA = row['Token']; // Cột đầu tiên - CA token
        const pairAddress = row['Pair Address'];
        let firstTime = row['First Time'];

        // Chuyển First Time về unix timestamp (giây)
        let fromTimestamp;
        if (typeof firstTime === 'number') {
            fromTimestamp = firstTime;
        } else {
            fromTimestamp = Math.floor(new Date(firstTime).getTime() / 1000);
        }

        // Nếu thiếu dữ liệu thì bỏ qua dòng đó
        if (!pairAddress || !fromTimestamp) {
            console.warn(`⚠️ Dòng ${index + 2}: Thiếu Pair Address hoặc First Time`);
            continue;
        }

        const body = {
            "network": "solana",
            "pair": pairAddress,
            "timestamp": fromTimestamp,
            "from": fromTimestamp,
            "to": toTimestamp,
            "vsToken": "USDC",
            "interval": 60,
            "cb": 400,
            "first": true,
            "isMC": true,
            //"_": new Date().toISOString()
        };

        // Lấy thêm các trường cần in từ Excel
        const symbol = row['Symbol'] || '';
        const signer = row['Signer'] || '';
        const athTimestampExcel = row['ATH Timestamp'] || '';
        // Định dạng lại thời gian First Time cho dễ nhìn
        const firstTimeStr = (typeof firstTime === 'number')
            ? new Date(firstTime * 1000).toISOString()
            : firstTime;

        console.log(`\n🔹 Dòng ${index + 2} | Pair: ${pairAddress} | From: ${fromTimestamp} | To: ${toTimestamp}`);
        console.log(`Symbol: ${symbol}`);
        console.log(`Signer: ${signer}`);
        console.log(`First Time: ${firstTimeStr}`);
        console.log(`ATH Timestamp Excel: ${athTimestampExcel}`);

        try {
            const response = await axios.post(
                'https://api.dex3.ai/ohlcv',
                body,
                {
                    headers: {
                        "Content-Type": "application/json",
                        // "Authorization": "Bearer YOUR_TOKEN_HERE"
                    }
                }
            );
            const candles = response.data?.data?.candles || [];

            // Lấy giá trị high lớn nhất và timestamp của nó trong candles
            let maxHigh = null;
            let maxHighTimestamp = null;
            if (candles.length > 0) {
                maxHigh = Math.max(...candles.map(candle => parseFloat(candle[2])));
                // Tìm candle có high bằng maxHigh
                const idx = candles.findIndex(candle => parseFloat(candle[2]) === maxHigh);
                if (idx !== -1) {
                    maxHighTimestamp = candles[idx][0]; // timestamp nằm ở vị trí 0
                }
            }

            const athMcapExcel = parseFloat(row['ATH MCap']);

            if (maxHigh !== null) {
                const diff = athMcapExcel !== 0 ? (maxHigh - athMcapExcel) / athMcapExcel * 100 : 0;
                const isNearCorrect = Math.abs(diff) <= 0.5;
                const maxHighTimeString = maxHighTimestamp
                    ? new Date(maxHighTimestamp * 1000).toISOString()
                    : 'N/A';

                // Lưu kết quả vào mảng
                const result = {
                    row: index + 2,
                    tokenCA,
                    pairAddress,
                    symbol,
                    signer,
                    fromTimestamp,
                    toTimestamp,
                    firstTime: firstTimeStr,
                    athTimestampExcel,
                    athMcapExcel,
                    maxHighAPI: maxHigh,
                    maxHighTimestamp: maxHighTimeString,
                    diffPercent: parseFloat(diff.toFixed(2)),
                    status: isNearCorrect ? 'GẦN ĐÚNG' : 'SAI',
                    isNearCorrect
                };

                // Nếu kết quả SAI, thử search pair khác từ token CA
                if (!isNearCorrect) {
                    console.log(`🔍 Kết quả SAI - Đang search pair khác cho token: ${tokenCA}`);
                    const searchResults = await searchPairByToken(tokenCA);

                    if (searchResults && searchResults.length > 0) {
                        console.log(`   ✅ Tìm thấy ${searchResults.length} pair(s) khác:`);
                        result.searchResults = searchResults;

                        // Hiển thị các pair tìm được
                        searchResults.forEach((pair, idx) => {
                            console.log(`   ${idx + 1}. ${pair.pairAddress} (${pair.dex}) - ${pair.symbol}`);
                        });

                        // Thử 5 pairs đầu tiên từ search results
                        const maxPairsToTry = Math.min(5, searchResults.length);
                        console.log(`   🔄 Đang thử ${maxPairsToTry} pairs đầu tiên (trong tổng số ${searchResults.length} pairs)...`);

                        let bestPair = null;
                        let bestDiff = Math.abs(diff);
                        let bestIsNearCorrect = isNearCorrect;

                        for (let i = 0; i < maxPairsToTry; i++) {
                            const alternatePair = searchResults[i];

                            // Bỏ qua nếu là pair gốc
                            if (alternatePair.pairAddress === pairAddress) {
                                console.log(`   ${i + 1}. ${alternatePair.pairAddress} (${alternatePair.dex}) - Pair gốc, bỏ qua`);
                                continue;
                            }

                            console.log(`   ${i + 1}. Thử ${alternatePair.pairAddress} (${alternatePair.dex})`);

                            try {
                                const alternateCandles = await fetchCandleData(alternatePair.pairAddress, fromTimestamp, toTimestamp);
                                console.log(`[fetchCandleData] pair: ${alternatePair.pairAddress}, from: ${fromTimestamp}, to: ${toTimestamp}`);
                                if (alternateCandles && alternateCandles.length > 0) {
                                    // Dữ liệu candle có format: [timestamp, open, high, low, close, volume]
                                    const alternateMaxHigh = Math.max(...alternateCandles.map(candle => parseFloat(candle[2])));
                                    const alternateMaxHighCandle = alternateCandles.find(candle => parseFloat(candle[2]) === alternateMaxHigh);
                                    const alternateMaxHighTimeString = new Date(alternateMaxHighCandle[0] * 1000).toISOString();

                                    const alternateDiff = ((alternateMaxHigh - athMcapExcel) / athMcapExcel) * 100;
                                    const alternateIsNearCorrect = Math.abs(alternateDiff) <= 5;

                                    console.log(`      📊 Max High: ${alternateMaxHigh.toLocaleString()}`);
                                    console.log(`      📊 Lệch: ${alternateDiff.toFixed(2)}% (${alternateIsNearCorrect ? '✅ GẦN ĐÚNG' : '❌ SAI'})`);

                                    // Kiểm tra xem có tốt hơn không
                                    if (alternateIsNearCorrect && !bestIsNearCorrect) {
                                        // Tìm được pair gần đúng đầu tiên
                                        bestPair = alternatePair;
                                        bestDiff = Math.abs(alternateDiff);
                                        bestIsNearCorrect = true;
                                        result.bestAlternateResult = {
                                            pairAddress: alternatePair.pairAddress,
                                            dex: alternatePair.dex,
                                            maxHighAPI: alternateMaxHigh,
                                            maxHighTimestamp: alternateMaxHighTimeString,
                                            diffPercent: parseFloat(alternateDiff.toFixed(2)),
                                            isNearCorrect: alternateIsNearCorrect
                                        };
                                        console.log(`      🎯 Tìm được pair GẦN ĐÚNG!`);
                                    } else if (alternateIsNearCorrect && bestIsNearCorrect && Math.abs(alternateDiff) < bestDiff) {
                                        // Tìm được pair gần đúng tốt hơn
                                        bestPair = alternatePair;
                                        bestDiff = Math.abs(alternateDiff);
                                        result.bestAlternateResult = {
                                            pairAddress: alternatePair.pairAddress,
                                            dex: alternatePair.dex,
                                            maxHighAPI: alternateMaxHigh,
                                            maxHighTimestamp: alternateMaxHighTimeString,
                                            diffPercent: parseFloat(alternateDiff.toFixed(2)),
                                            isNearCorrect: alternateIsNearCorrect
                                        };
                                        console.log(`      🎯 Tìm được pair GẦN ĐÚNG tốt hơn!`);
                                    } else if (!bestIsNearCorrect && Math.abs(alternateDiff) < bestDiff) {
                                        // Chưa có pair gần đúng, nhưng tìm được pair ít lệch hơn
                                        bestPair = alternatePair;
                                        bestDiff = Math.abs(alternateDiff);
                                        result.bestAlternateResult = {
                                            pairAddress: alternatePair.pairAddress,
                                            dex: alternatePair.dex,
                                            maxHighAPI: alternateMaxHigh,
                                            maxHighTimestamp: alternateMaxHighTimeString,
                                            diffPercent: parseFloat(alternateDiff.toFixed(2)),
                                            isNearCorrect: alternateIsNearCorrect
                                        };
                                        console.log(`      📈 Tìm được pair ít lệch hơn`);
                                    }
                                } else {
                                    console.log(`      ❌ Không có dữ liệu candle`);
                                }
                            } catch (err) {
                                console.log(`      ❌ Lỗi: ${err.message}`);
                            }
                        }

                        // Cập nhật kết quả nếu tìm được pair tốt hơn
                        if (bestPair && (bestIsNearCorrect || bestDiff < Math.abs(diff))) {
                            console.log(`   🎉 Cập nhật với pair tốt nhất: ${bestPair.pairAddress} (${bestPair.dex})`);
                            result.pairAddress = bestPair.pairAddress;
                            result.dex = bestPair.dex;
                            result.maxHighAPI = result.bestAlternateResult.maxHighAPI;
                            result.maxHighTimestamp = result.bestAlternateResult.maxHighTimestamp;
                            result.diffPercent = result.bestAlternateResult.diffPercent;
                            result.status = result.bestAlternateResult.isNearCorrect ? 'GẦN ĐÚNG' : 'SAI';
                            result.isNearCorrect = result.bestAlternateResult.isNearCorrect;
                            result.originalPairAddress = pairAddress;
                            result.replacedWithBetterPair = true;
                        }
                    } else {
                        console.log(`   ❌ Không tìm thấy pair nào khác cho token này`);
                        result.searchResults = [];
                    }
                }

                results.push(result);

                if (!isNearCorrect) {
                    console.log(`ATH MCap Excel:     ${athMcapExcel}`);
                    console.log(`Max High API:       ${maxHigh}`);
                    console.log(`Thời gian Max High: ${maxHighTimeString}`);
                    console.log(`Lệch:               ${diff.toFixed(2)}% (❌ SAI)`);
                }
            } else {
                console.log('⚠️ Không tìm thấy candle nào để lấy high!');

                // Lưu kết quả lỗi vào mảng
                const result = {
                    row: index + 2,
                    tokenCA,
                    pairAddress,
                    symbol,
                    signer,
                    fromTimestamp,
                    toTimestamp,
                    firstTime: firstTimeStr,
                    athTimestampExcel,
                    athMcapExcel,
                    maxHighAPI: null,
                    maxHighTimestamp: null,
                    diffPercent: null,
                    status: 'LỖI - KHÔNG TÌM THẤY CANDLE',
                    isNearCorrect: false
                };
                results.push(result);
            }

        } catch (err) {
            if (err.response && err.response.data) {
                console.error('❌ Lỗi API:', JSON.stringify(err.response.data));
            } else {
                console.error('❌ Lỗi khác:', err.message);
            }

            // Lưu kết quả lỗi API vào mảng
            const result = {
                row: index + 2,
                tokenCA,
                pairAddress,
                symbol,
                signer,
                fromTimestamp,
                toTimestamp,
                firstTime: firstTimeStr,
                athTimestampExcel,
                athMcapExcel: parseFloat(row['ATH MCap']) || null,
                maxHighAPI: null,
                maxHighTimestamp: null,
                diffPercent: null,
                status: 'LỖI API',
                isNearCorrect: false,
                error: err.message
            };
            results.push(result);
        }
    }

    // Xuất kết quả ra file JSON
    const outputData = {
        metadata: {
            timestamp: new Date().toISOString(),
            totalRows: results.length,
            summary: {
                nearCorrect: results.filter(r => r.isNearCorrect).length,
                incorrect: results.filter(r => r.status === 'SAI').length,
                errors: results.filter(r => r.status.includes('LỖI')).length
            }
        },
        results: results
    };

    const fileName = `comparison_results_firstTime.json`;
    fs.writeFileSync(fileName, JSON.stringify(outputData, null, 2), 'utf8');

    console.log(`\n📊 TỔNG KẾT:`);
    console.log(`✅ Gần đúng: ${outputData.metadata.summary.nearCorrect}`);
    console.log(`❌ Sai: ${outputData.metadata.summary.incorrect}`);
    console.log(`⚠️ Lỗi: ${outputData.metadata.summary.errors}`);
    console.log(`📁 Đã xuất kết quả ra file: ${fileName}`);
}

callApiForEachRow();
