{"name": "excel-api-data-comparison", "version": "1.0.0", "description": "Tool to compare ATH Market Cap data between Excel files and dex3.ai API with intelligent pair search and replacement", "main": "compareExcelWithApi.js", "scripts": {"start": "node compareExcelWithApi.js", "test": "node testSearchAPI.js", "compare": "node compareExcelWithApi.js", "search-test": "node testSearchAPI.js", "help": "echo 'Available commands: npm start (run comparison), npm test (test search API)'"}, "keywords": ["excel", "api", "comparison", "crypto", "defi", "market-cap", "dex", "trading", "data-analysis", "automation", "dex3.ai", "solana", "pump-fun", "raydium", "meteora"], "author": "Augment Agent", "license": "MIT", "dependencies": {"axios": "^1.9.0", "xlsx": "^0.18.5"}, "engines": {"node": ">=14.0.0", "npm": ">=6.0.0"}, "config": {"threshold": 0.5, "outputFile": "comparison_results_firstTime.json", "inputFile": "data.xlsx"}, "files": ["compareExcelWithApi.js", "testSearchAPI.js"]}